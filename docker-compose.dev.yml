version: '3.8'

# 开发环境配置 - 覆盖生产配置
services:
  # 数据库服务 - 开发配置
  database:
    environment:
      POSTGRES_DB: zentao_mcp_dev
      POSTGRES_USER: dev_user
      POSTGRES_PASSWORD: dev_password
    ports:
      - "5433:5432"  # 避免与本地PostgreSQL冲突

  # 后端服务 - 开发配置
  backend:
    build:
      context: ./zentao-mcp-backend-service
      dockerfile: Dockerfile.dev
      target: development
    environment:
      - DATABASE_URL=************************************************/zentao_mcp_dev
      - SECRET_KEY=dev-secret-key-not-for-production
      - DEBUG=true
      - CORS_ORIGINS=http://localhost:3000,http://localhost:5173
      - LOG_LEVEL=DEBUG
    volumes:
      - ./zentao-mcp-backend-service:/app:ro
      - backend_dev_logs:/app/logs
    ports:
      - "8001:8000"  # 开发端口
    command: ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]

  # 前端服务 - 开发配置
  frontend:
    build:
      context: ./zentao-mcp-admin-web
      dockerfile: Dockerfile.dev
      target: development
    environment:
      - NODE_ENV=development
      - VITE_API_BASE_URL=http://localhost:8001
    volumes:
      - ./zentao-mcp-admin-web:/app:ro
      - /app/node_modules
    ports:
      - "5173:5173"  # Vite开发服务器端口
    command: ["npm", "run", "dev", "--", "--host", "0.0.0.0"]

  # 开发工具容器
  dev-tools:
    image: node:18-alpine
    container_name: zentao-mcp-dev-tools
    working_dir: /workspace
    volumes:
      - .:/workspace
    networks:
      - zentao-mcp-network
    command: ["tail", "-f", "/dev/null"]  # 保持容器运行
    profiles:
      - tools

# 开发环境专用卷
volumes:
  backend_dev_logs:
    driver: local
