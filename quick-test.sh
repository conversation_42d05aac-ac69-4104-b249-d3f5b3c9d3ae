#!/bin/bash

# 禅道MCP服务快速测试脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 测试后端服务
test_backend() {
    log_info "测试后端服务..."
    
    local backend_url="http://localhost:8000"
    local max_attempts=30
    local attempt=1
    
    # 等待服务启动
    while [[ $attempt -le $max_attempts ]]; do
        if curl -f "$backend_url/health" &> /dev/null; then
            log_success "后端服务健康检查通过"
            break
        fi
        
        log_info "等待后端服务启动... ($attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done
    
    if [[ $attempt -gt $max_attempts ]]; then
        log_error "后端服务启动超时"
        return 1
    fi
    
    # 测试API文档
    if curl -f "$backend_url/docs" &> /dev/null; then
        log_success "API文档访问正常"
    else
        log_warning "API文档访问失败"
    fi
    
    # 测试OpenAPI规范
    if curl -f "$backend_url/openapi.json" &> /dev/null; then
        log_success "OpenAPI规范访问正常"
    else
        log_warning "OpenAPI规范访问失败"
    fi
    
    return 0
}

# 测试前端服务
test_frontend() {
    log_info "测试前端服务..."
    
    local frontend_url="http://localhost:3000"
    local max_attempts=30
    local attempt=1
    
    # 等待服务启动
    while [[ $attempt -le $max_attempts ]]; do
        if curl -f "$frontend_url" &> /dev/null; then
            log_success "前端服务访问正常"
            break
        fi
        
        log_info "等待前端服务启动... ($attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done
    
    if [[ $attempt -gt $max_attempts ]]; then
        log_error "前端服务启动超时"
        return 1
    fi
    
    return 0
}

# 测试数据库连接
test_database() {
    log_info "测试数据库连接..."
    
    # 通过后端API测试数据库连接
    local response=$(curl -s "http://localhost:8000/health" 2>/dev/null || echo "")
    
    if [[ -n "$response" ]] && echo "$response" | grep -q "healthy"; then
        log_success "数据库连接正常"
        return 0
    else
        log_error "数据库连接失败"
        return 1
    fi
}

# 测试客户端
test_client() {
    log_info "测试客户端..."
    
    local client_dir="zentao-mcp-client"
    
    if [[ ! -d "$client_dir" ]]; then
        log_warning "客户端目录不存在，跳过客户端测试"
        return 0
    fi
    
    cd "$client_dir"
    
    # 运行客户端测试
    if python -m pytest tests/ -v --tb=short; then
        log_success "客户端测试通过"
    else
        log_error "客户端测试失败"
        cd ..
        return 1
    fi
    
    cd ..
    return 0
}

# 测试后端单元测试
test_backend_unit() {
    log_info "运行后端单元测试..."
    
    local backend_dir="zentao-mcp-backend-service"
    
    if [[ ! -d "$backend_dir" ]]; then
        log_warning "后端目录不存在，跳过后端测试"
        return 0
    fi
    
    cd "$backend_dir"
    
    # 运行后端测试
    if python -m pytest tests/ -v --tb=short; then
        log_success "后端单元测试通过"
    else
        log_error "后端单元测试失败"
        cd ..
        return 1
    fi
    
    cd ..
    return 0
}

# 测试MCP工具
test_mcp_tools() {
    log_info "测试MCP工具..."
    
    local backend_url="http://localhost:8000"
    local test_tools=(
        "mcp_get_health_status"
        "mcp_get_performance_metrics"
    )
    
    for tool in "${test_tools[@]}"; do
        log_info "测试工具: $tool"
        
        local response=$(curl -s -X POST \
            "$backend_url/api/v1/mcp/tools/$tool" \
            -H "Content-Type: application/json" \
            -d '{}' 2>/dev/null || echo "")
        
        if [[ -n "$response" ]]; then
            # 检查是否返回了有效的JSON
            if echo "$response" | python -m json.tool &> /dev/null; then
                log_success "工具 $tool 响应正常"
            else
                log_warning "工具 $tool 响应格式异常"
            fi
        else
            log_warning "工具 $tool 无响应"
        fi
    done
}

# 生成测试报告
generate_report() {
    local results=("$@")
    local total=${#results[@]}
    local passed=0
    
    for result in "${results[@]}"; do
        if [[ "$result" == "0" ]]; then
            ((passed++))
        fi
    done
    
    local failed=$((total - passed))
    
    echo
    log_info "测试报告"
    echo "===================="
    echo "总测试项: $total"
    echo "通过: $passed"
    echo "失败: $failed"
    echo "成功率: $(( passed * 100 / total ))%"
    echo
    
    if [[ $failed -eq 0 ]]; then
        log_success "所有测试通过！"
        return 0
    else
        log_error "部分测试失败"
        return 1
    fi
}

# 显示帮助
show_help() {
    cat << EOF
禅道MCP服务快速测试脚本

用法: $0 [选项] [测试类型]

测试类型:
    all         运行所有测试 (默认)
    backend     仅测试后端服务
    frontend    仅测试前端服务
    client      仅测试客户端
    unit        仅运行单元测试
    integration 仅运行集成测试

选项:
    -h, --help      显示帮助信息
    -v, --verbose   详细输出
    --no-wait       不等待服务启动

示例:
    $0              # 运行所有测试
    $0 backend      # 仅测试后端
    $0 unit         # 仅运行单元测试
EOF
}

# 主函数
main() {
    local test_type="all"
    local verbose=false
    local no_wait=false
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -v|--verbose)
                verbose=true
                set -x
                shift
                ;;
            --no-wait)
                no_wait=true
                shift
                ;;
            all|backend|frontend|client|unit|integration)
                test_type=$1
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    log_info "开始禅道MCP服务测试..."
    log_info "测试类型: $test_type"
    
    # 等待服务启动
    if [[ "$no_wait" != "true" && ("$test_type" == "all" || "$test_type" == "integration") ]]; then
        log_info "等待服务启动..."
        sleep 10
    fi
    
    local results=()
    
    # 根据测试类型执行测试
    case $test_type in
        all)
            test_backend; results+=($?)
            test_frontend; results+=($?)
            test_database; results+=($?)
            test_mcp_tools; results+=($?)
            test_backend_unit; results+=($?)
            test_client; results+=($?)
            ;;
        backend)
            test_backend; results+=($?)
            test_database; results+=($?)
            test_mcp_tools; results+=($?)
            ;;
        frontend)
            test_frontend; results+=($?)
            ;;
        client)
            test_client; results+=($?)
            ;;
        unit)
            test_backend_unit; results+=($?)
            test_client; results+=($?)
            ;;
        integration)
            test_backend; results+=($?)
            test_frontend; results+=($?)
            test_database; results+=($?)
            test_mcp_tools; results+=($?)
            ;;
    esac
    
    # 生成测试报告
    generate_report "${results[@]}"
}

# 执行主函数
main "$@"
