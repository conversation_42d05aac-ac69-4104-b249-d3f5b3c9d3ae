#!/usr/bin/env python3
"""
端到端集成测试 - 验证前后端和客户端的完整集成
"""

import asyncio
import subprocess
import time
import json
import httpx
import os
import signal
from pathlib import Path
from typing import Dict, Any, List, Optional


class E2ETestRunner:
    """端到端测试运行器"""
    
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.backend_url = "http://localhost:8000"
        self.frontend_url = "http://localhost:3000"
        self.client_proxy_url = "http://localhost:8080"
        
        self.processes = {}
        self.test_results = []
        
    async def setup_test_environment(self):
        """设置测试环境"""
        print("🔧 设置端到端测试环境...")
        
        # 1. 启动后端服务
        print("启动后端服务...")
        backend_process = await self.start_backend_service()
        if backend_process:
            self.processes['backend'] = backend_process
            await self.wait_for_service(self.backend_url, "后端服务")
        
        # 2. 启动前端服务
        print("启动前端服务...")
        frontend_process = await self.start_frontend_service()
        if frontend_process:
            self.processes['frontend'] = frontend_process
            await self.wait_for_service(self.frontend_url, "前端服务")
        
        # 3. 配置并启动客户端代理
        print("配置并启动客户端代理...")
        await self.setup_client_config()
        client_process = await self.start_client_proxy()
        if client_process:
            self.processes['client'] = client_process
            await self.wait_for_service(self.client_proxy_url, "客户端代理")
    
    async def start_backend_service(self) -> Optional[subprocess.Popen]:
        """启动后端服务"""
        try:
            backend_dir = self.base_dir / "zentao-mcp-backend-service"
            if not backend_dir.exists():
                print("❌ 后端服务目录不存在")
                return None
            
            # 启动后端服务
            process = subprocess.Popen(
                ["python", "-m", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"],
                cwd=backend_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            return process
            
        except Exception as e:
            print(f"❌ 启动后端服务失败: {e}")
            return None
    
    async def start_frontend_service(self) -> Optional[subprocess.Popen]:
        """启动前端服务"""
        try:
            frontend_dir = self.base_dir / "zentao-mcp-admin-web"
            if not frontend_dir.exists():
                print("❌ 前端服务目录不存在")
                return None
            
            # 启动前端开发服务器
            process = subprocess.Popen(
                ["npm", "run", "dev", "--", "--host", "0.0.0.0", "--port", "3000"],
                cwd=frontend_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            return process
            
        except Exception as e:
            print(f"❌ 启动前端服务失败: {e}")
            return None
    
    async def setup_client_config(self):
        """设置客户端配置"""
        try:
            client_dir = self.base_dir / "zentao-mcp-client"
            
            # 运行配置命令
            process = subprocess.Popen(
                ["python", "-m", "zentao_mcp_client.cli", "configure"],
                cwd=client_dir,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # 提供配置输入
            stdout, stderr = process.communicate(
                input=f"{self.backend_url}\ntest-e2e-api-key\n"
            )
            
            if process.returncode == 0:
                print("✅ 客户端配置成功")
            else:
                print(f"⚠️  客户端配置警告: {stderr}")
                
        except Exception as e:
            print(f"❌ 客户端配置失败: {e}")
    
    async def start_client_proxy(self) -> Optional[subprocess.Popen]:
        """启动客户端代理"""
        try:
            client_dir = self.base_dir / "zentao-mcp-client"
            
            # 启动HTTP模式的客户端代理
            process = subprocess.Popen(
                ["python", "-m", "zentao_mcp_client.cli", "start", "--mode", "http", "--port", "8080"],
                cwd=client_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            return process
            
        except Exception as e:
            print(f"❌ 启动客户端代理失败: {e}")
            return None
    
    async def wait_for_service(self, url: str, service_name: str, timeout: int = 30):
        """等待服务启动"""
        print(f"等待 {service_name} 启动...")
        
        async with httpx.AsyncClient() as client:
            for i in range(timeout):
                try:
                    response = await client.get(url, timeout=2.0)
                    if response.status_code < 500:
                        print(f"✅ {service_name} 已启动")
                        return True
                except:
                    pass
                
                await asyncio.sleep(1)
        
        print(f"⚠️  {service_name} 启动超时")
        return False
    
    async def run_integration_tests(self):
        """运行集成测试"""
        print("\n🧪 开始端到端集成测试...")
        
        tests = [
            ("后端健康检查", self.test_backend_health),
            ("前端页面访问", self.test_frontend_access),
            ("客户端代理健康检查", self.test_client_proxy_health),
            ("后端API认证", self.test_backend_auth),
            ("客户端工具调用", self.test_client_tool_calls),
            ("前后端集成", self.test_frontend_backend_integration),
            ("完整工作流程", self.test_complete_workflow),
        ]
        
        for test_name, test_func in tests:
            print(f"\n📋 测试: {test_name}")
            try:
                result = await test_func()
                self.test_results.append({
                    "name": test_name,
                    "success": result,
                    "timestamp": time.time()
                })
                
                if result:
                    print(f"✅ {test_name} - 通过")
                else:
                    print(f"❌ {test_name} - 失败")
                    
            except Exception as e:
                print(f"💥 {test_name} - 异常: {e}")
                self.test_results.append({
                    "name": test_name,
                    "success": False,
                    "error": str(e),
                    "timestamp": time.time()
                })
    
    async def test_backend_health(self) -> bool:
        """测试后端健康检查"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{self.backend_url}/health")
                return response.status_code == 200
        except:
            return False
    
    async def test_frontend_access(self) -> bool:
        """测试前端页面访问"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(self.frontend_url)
                return response.status_code == 200
        except:
            return False
    
    async def test_client_proxy_health(self) -> bool:
        """测试客户端代理健康检查"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{self.client_proxy_url}/health")
                return response.status_code == 200
        except:
            return False
    
    async def test_backend_auth(self) -> bool:
        """测试后端API认证"""
        try:
            async with httpx.AsyncClient() as client:
                # 测试无认证访问
                response = await client.get(f"{self.backend_url}/api/v1/admin/users")
                if response.status_code != 403:
                    return False
                
                # 测试有认证访问（需要先创建API Key）
                return True
        except:
            return False
    
    async def test_client_tool_calls(self) -> bool:
        """测试客户端工具调用"""
        try:
            async with httpx.AsyncClient() as client:
                # 测试通过客户端代理调用工具
                response = await client.post(
                    f"{self.client_proxy_url}/mcp/tools/mcp_get_health_status",
                    json={}
                )
                return response.status_code in [200, 401, 403]  # 可能因为认证失败，但接口存在
        except:
            return False
    
    async def test_frontend_backend_integration(self) -> bool:
        """测试前后端集成"""
        try:
            # 这里可以添加更复杂的前后端交互测试
            # 比如模拟登录、API调用等
            return True
        except:
            return False
    
    async def test_complete_workflow(self) -> bool:
        """测试完整工作流程"""
        try:
            # 模拟完整的用户工作流程
            # 1. 前端登录
            # 2. 创建API Key
            # 3. 客户端使用API Key调用工具
            # 4. 查看审计日志
            return True
        except:
            return False
    
    def cleanup(self):
        """清理测试环境"""
        print("\n🧹 清理测试环境...")
        
        for service_name, process in self.processes.items():
            try:
                print(f"停止 {service_name}...")
                process.terminate()
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                print(f"强制停止 {service_name}...")
                process.kill()
            except Exception as e:
                print(f"停止 {service_name} 时出错: {e}")
    
    def generate_report(self):
        """生成测试报告"""
        print("\n📊 端到端测试报告")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r["success"]])
        failed_tests = total_tests - passed_tests
        
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests}")
        print(f"失败: {failed_tests}")
        print(f"成功率: {passed_tests/total_tests*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ 失败的测试:")
            for result in self.test_results:
                if not result["success"]:
                    error_msg = result.get("error", "未知错误")
                    print(f"  - {result['name']}: {error_msg}")
        
        # 保存详细报告
        report_data = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "summary": {
                "total": total_tests,
                "passed": passed_tests,
                "failed": failed_tests,
                "success_rate": passed_tests/total_tests*100
            },
            "results": self.test_results
        }
        
        with open("e2e_test_report.json", "w", encoding="utf-8") as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 详细报告已保存到: e2e_test_report.json")
        
        return passed_tests/total_tests >= 0.8


async def main():
    """主函数"""
    runner = E2ETestRunner()
    
    try:
        # 设置测试环境
        await runner.setup_test_environment()
        
        # 等待服务稳定
        await asyncio.sleep(5)
        
        # 运行集成测试
        await runner.run_integration_tests()
        
        # 生成报告
        success = runner.generate_report()
        
        return success
        
    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
        return False
    except Exception as e:
        print(f"\n💥 测试执行失败: {e}")
        return False
    finally:
        # 清理环境
        runner.cleanup()


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
