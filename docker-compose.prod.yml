version: '3.8'

# 生产环境配置 - 覆盖基础配置
services:
  # 数据库服务 - 生产配置
  database:
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-zentao_mcp_prod}
      POSTGRES_USER: ${POSTGRES_USER:-zentao_mcp_prod}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_prod_data:/var/lib/postgresql/data
      - ./backups:/backups
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'

  # 后端服务 - 生产配置
  backend:
    environment:
      - DATABASE_URL=postgresql://${POSTGRES_USER:-zentao_mcp_prod}:${POSTGRES_PASSWORD}@database:5432/${POSTGRES_DB:-zentao_mcp_prod}
      - SECRET_KEY=${SECRET_KEY}
      - DEBUG=false
      - CORS_ORIGINS=${CORS_ORIGINS:-https://yourdomain.com}
      - LOG_LEVEL=INFO
      - SENTRY_DSN=${SENTRY_DSN}
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # 前端服务 - 生产配置
  frontend:
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'
        reservations:
          memory: 128M
          cpus: '0.1'
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Redis - 生产配置
  redis:
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    volumes:
      - redis_prod_data:/data
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'
        reservations:
          memory: 128M
          cpus: '0.1'

  # Nginx - 生产配置
  nginx:
    volumes:
      - ./nginx/prod.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx_prod_logs:/var/log/nginx
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.25'
        reservations:
          memory: 128M
          cpus: '0.1'

  # 监控服务
  prometheus:
    image: prom/prometheus:latest
    container_name: zentao-mcp-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    networks:
      - zentao-mcp-network
    profiles:
      - monitoring

  grafana:
    image: grafana/grafana:latest
    container_name: zentao-mcp-grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana:/etc/grafana/provisioning
    networks:
      - zentao-mcp-network
    profiles:
      - monitoring

  # 日志收集
  fluentd:
    image: fluent/fluentd:v1.16-debian-1
    container_name: zentao-mcp-fluentd
    volumes:
      - ./logging/fluentd.conf:/fluentd/etc/fluent.conf:ro
      - backend_logs:/var/log/backend:ro
      - nginx_prod_logs:/var/log/nginx:ro
    networks:
      - zentao-mcp-network
    profiles:
      - logging

# 生产环境数据卷
volumes:
  postgres_prod_data:
    driver: local
  redis_prod_data:
    driver: local
  nginx_prod_logs:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
