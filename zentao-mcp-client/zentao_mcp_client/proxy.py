"""
核心代理逻辑模块 - 使用FastMCP SDK
"""

import asyncio
import logging
import httpx
from typing import Any, Dict, List, Optional
from fastmcp import FastMCP
from .config import ClientConfig

logger = logging.getLogger(__name__)


class ZentaoMCPProxy:
    """Zentao MCP代理服务"""

    def __init__(self, config: ClientConfig):
        self.config = config
        self.backend_url = config.get_backend_url()
        self.api_key = config.get_api_key()
        self.client = httpx.AsyncClient(timeout=30.0)

        # 工具端点映射表 - 修复API不匹配问题
        self.tool_endpoints = {
            # 部门相关工具
            "zentao_get_all_departments": "/api/v1/mcp/tools/zentao_get_all_departments",
            "zentao_get_users_by_department": "/api/v1/mcp/tools/zentao_get_users_by_department",

            # 项目相关工具
            "zentao_get_all_projects": "/api/v1/mcp/tools/zentao_get_all_projects",
            "zentao_get_project_detail": "/api/v1/mcp/tools/zentao_get_project_detail",
            "zentao_get_stories_by_project": "/api/v1/mcp/tools/zentao_get_stories_by_project",
            "zentao_get_tasks_by_project": "/api/v1/mcp/tools/zentao_get_tasks_by_project",
            "zentao_get_bugs_by_project": "/api/v1/mcp/tools/zentao_get_bugs_by_project",

            # Bug相关工具
            "zentao_get_bug_detail": "/api/v1/mcp/tools/zentao_get_bug_detail",
            "zentao_get_bugs_by_time_and_dept": "/api/v1/mcp/tools/zentao_get_bugs_by_time_and_dept",

            # 需求相关工具
            "zentao_get_story_detail": "/api/v1/mcp/tools/zentao_get_story_detail",

            # 任务相关工具
            "zentao_get_task_detail": "/api/v1/mcp/tools/zentao_get_task_detail",
            "zentao_get_tasks_by_account": "/api/v1/mcp/tools/zentao_get_tasks_by_account",
            "zentao_get_tasks_by_dept": "/api/v1/mcp/tools/zentao_get_tasks_by_dept",

            # 用户相关工具
            "zentao_get_user_info": "/api/v1/mcp/tools/zentao_get_user_info",
            "zentao_get_users_by_account": "/api/v1/mcp/tools/zentao_get_users_by_account",

            # 分析相关工具
            "analyze_story_workload": "/api/v1/mcp/tools/analyze_story_workload",
            "analyze_bugs_by_dept_and_time": "/api/v1/mcp/tools/analyze_bugs_by_dept_and_time",
            "project_summary_analysis": "/api/v1/mcp/tools/project_summary_analysis",
            "personnel_workload_analysis": "/api/v1/mcp/tools/personnel_workload_analysis",
            "story_task_relation_query": "/api/v1/mcp/tools/story_task_relation_query",
            "bug_to_story_tracking": "/api/v1/mcp/tools/bug_to_story_tracking",
            "filter_bugs_by_criteria": "/api/v1/mcp/tools/filter_bugs_by_criteria",
            "batch_query_stories": "/api/v1/mcp/tools/batch_query_stories",
            "validate_story_existence": "/api/v1/mcp/tools/validate_story_existence",

            # 系统工具
            "mcp_get_health_status": "/api/v1/mcp/tools/mcp_get_health_status",
            "mcp_get_performance_metrics": "/api/v1/mcp/tools/mcp_get_performance_metrics",
        }

        # 初始化FastMCP
        self.mcp = FastMCP("Zentao MCP Client")

        # 注册工具
        self._register_tools()

        # 注册资源
        self._register_resources()
    
    def _register_tools(self):
        """注册所有工具"""

        # 部门相关工具
        @self.mcp.tool()
        async def zentao_get_all_departments() -> List[Dict[str, Any]]:
            """获取所有部门列表"""
            return await self._forward_request("zentao_get_all_departments", {})

        @self.mcp.tool()
        async def zentao_get_users_by_department(dept_id: int) -> List[Dict[str, Any]]:
            """根据部门ID获取用户列表"""
            return await self._forward_request("zentao_get_users_by_department", {"dept_id": dept_id})

        # 项目相关工具
        @self.mcp.tool()
        async def zentao_get_all_projects() -> List[Dict[str, Any]]:
            """获取所有项目列表"""
            return await self._forward_request("zentao_get_all_projects", {})
        
        @self.mcp.tool()
        async def zentao_get_project_detail(project_id: int) -> Dict[str, Any]:
            """获取项目详情"""
            return await self._forward_request("zentao_get_project_detail", {"project_id": project_id})
        
        @self.mcp.tool()
        async def zentao_get_stories_by_project(project_id: int) -> List[Dict[str, Any]]:
            """根据项目ID获取需求列表"""
            return await self._forward_request("zentao_get_stories_by_project", {"project_id": project_id})

        @self.mcp.tool()
        async def zentao_get_tasks_by_project(project_id: int) -> List[Dict[str, Any]]:
            """根据项目ID获取任务列表"""
            return await self._forward_request("zentao_get_tasks_by_project", {"project_id": project_id})

        @self.mcp.tool()
        async def zentao_get_bugs_by_project(project_id: int) -> List[Dict[str, Any]]:
            """根据项目ID获取Bug列表"""
            return await self._forward_request("zentao_get_bugs_by_project", {"project_id": project_id})

        # Bug相关工具
        @self.mcp.tool()
        async def zentao_get_bugs_by_time_and_dept(start_date: str, end_date: str, dept_id: int) -> List[Dict[str, Any]]:
            """根据时间范围和部门查询Bug"""
            return await self._forward_request("zentao_get_bugs_by_time_and_dept", {
                "start_date": start_date,
                "end_date": end_date,
                "dept_id": dept_id
            })

        @self.mcp.tool()
        async def zentao_get_bug_detail(bug_id: int) -> Dict[str, Any]:
            """获取Bug详情"""
            return await self._forward_request("zentao_get_bug_detail", {"bug_id": bug_id})

        # 需求相关工具
        @self.mcp.tool()
        async def zentao_get_story_detail(story_id: int) -> Dict[str, Any]:
            """获取需求详情"""
            return await self._forward_request("zentao_get_story_detail", {"story_id": story_id})

        # 任务相关工具
        @self.mcp.tool()
        async def zentao_get_task_detail(task_id: int) -> Dict[str, Any]:
            """获取任务详情"""
            return await self._forward_request("zentao_get_task_detail", {"task_id": task_id})

        @self.mcp.tool()
        async def zentao_get_tasks_by_account(account: str, start_date: str, end_date: str, is_doing: bool = False) -> List[Dict[str, Any]]:
            """根据域账号查询任务"""
            return await self._forward_request("zentao_get_tasks_by_account", {
                "account": account,
                "start_date": start_date,
                "end_date": end_date,
                "is_doing": is_doing
            })

        @self.mcp.tool()
        async def zentao_get_tasks_by_dept(dept_id: int, start_date: str, end_date: str) -> List[Dict[str, Any]]:
            """根据部门查询任务"""
            return await self._forward_request("zentao_get_tasks_by_dept", {
                "dept_id": dept_id,
                "start_date": start_date,
                "end_date": end_date
            })

        # 用户相关工具
        @self.mcp.tool()
        async def zentao_get_user_info(user_account: str) -> Dict[str, Any]:
            """获取用户信息"""
            return await self._forward_request("zentao_get_user_info", {"user_account": user_account})

        @self.mcp.tool()
        async def zentao_get_users_by_account(accounts: List[str]) -> List[Dict[str, Any]]:
            """根据账号列表获取用户信息"""
            return await self._forward_request("zentao_get_users_by_account", {"accounts": accounts})

        # 分析相关工具
        @self.mcp.tool()
        async def analyze_story_workload(story_ids: List[int]) -> Dict[str, Any]:
            """分析需求工作量"""
            return await self._forward_request("analyze_story_workload", {"story_ids": story_ids})

        @self.mcp.tool()
        async def analyze_bugs_by_dept_and_time(dept_id: int, start_date: str, end_date: str) -> Dict[str, Any]:
            """按部门和时间分析Bug"""
            return await self._forward_request("analyze_bugs_by_dept_and_time", {
                "dept_id": dept_id,
                "start_date": start_date,
                "end_date": end_date
            })

        @self.mcp.tool()
        async def project_summary_analysis(project_id: int) -> Dict[str, Any]:
            """项目汇总分析"""
            return await self._forward_request("project_summary_analysis", {"project_id": project_id})

        @self.mcp.tool()
        async def personnel_workload_analysis(accounts: List[str], start_date: str, end_date: str) -> Dict[str, Any]:
            """人员工作量分析"""
            return await self._forward_request("personnel_workload_analysis", {
                "accounts": accounts,
                "start_date": start_date,
                "end_date": end_date
            })

        @self.mcp.tool()
        async def story_task_relation_query(story_ids: Optional[List[int]] = None, project_id: Optional[int] = None) -> Dict[str, Any]:
            """需求-任务关联查询"""
            return await self._forward_request("story_task_relation_query", {
                "story_ids": story_ids,
                "project_id": project_id
            })

        @self.mcp.tool()
        async def bug_to_story_tracking(bug_ids: List[int]) -> Dict[str, Any]:
            """Bug到需求追踪"""
            return await self._forward_request("bug_to_story_tracking", {"bug_ids": bug_ids})

        @self.mcp.tool()
        async def filter_bugs_by_criteria(criteria: Dict[str, Any]) -> List[Dict[str, Any]]:
            """按条件筛选Bug"""
            return await self._forward_request("filter_bugs_by_criteria", {"criteria": criteria})

        @self.mcp.tool()
        async def batch_query_stories(story_ids: List[int]) -> Dict[str, Any]:
            """批量查询需求信息"""
            return await self._forward_request("batch_query_stories", {"story_ids": story_ids})

        @self.mcp.tool()
        async def validate_story_existence(story_ids: List[int]) -> Dict[str, Any]:
            """验证需求是否存在"""
            return await self._forward_request("validate_story_existence", {"story_ids": story_ids})

        # 系统工具
        @self.mcp.tool()
        async def mcp_get_health_status() -> Dict[str, Any]:
            """获取服务健康状态"""
            return await self._forward_request("mcp_get_health_status", {})

        @self.mcp.tool()
        async def mcp_get_performance_metrics() -> Dict[str, Any]:
            """获取性能指标"""
            return await self._forward_request("mcp_get_performance_metrics", {})
    
    def _register_resources(self):
        """注册资源"""

        @self.mcp.resource("zentao://departments")
        async def departments_resource() -> str:
            """部门资源"""
            result = await self._forward_request("zentao_get_all_departments", {})
            return str(result)

        @self.mcp.resource("zentao://projects")
        async def projects_resource() -> str:
            """项目资源"""
            result = await self._forward_request("zentao_get_all_projects", {})
            return str(result)

    async def _forward_request(self, tool_name: str, arguments: Dict[str, Any]) -> Any:
        """转发请求到后端服务 - 修复后的版本"""
        try:
            # 获取对应的端点
            endpoint = self.tool_endpoints.get(tool_name)
            if not endpoint:
                raise Exception(f"不支持的工具: {tool_name}")

            # 构建请求头
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }

            # 发送请求到具体端点
            response = await self.client.post(
                f"{self.backend_url}{endpoint}",
                json=arguments,  # 直接传递参数，不包装
                headers=headers
            )

            response.raise_for_status()
            result = response.json()

            # 直接返回结果，后端已经是标准格式
            return result

        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP请求失败: {e.response.status_code} - {e.response.text}")
            # 尝试解析错误响应
            try:
                error_detail = e.response.json()
                raise Exception(f"后端服务错误: {error_detail.get('detail', '未知错误')}")
            except:
                raise Exception(f"后端服务错误: {e.response.status_code}")
        except httpx.ConnectError as e:
            logger.error(f"转发请求失败: {e}")
            raise Exception(f"转发请求失败: Connection failed")
        except Exception as e:
            logger.error(f"转发请求失败: {e}")
            raise
    
    async def close(self):
        """关闭代理服务"""
        await self.client.aclose()

    async def start_http_server(self, host: str, port: int):
        """启动HTTP服务器模式"""
        try:
            from fastapi import FastAPI, HTTPException
            from fastapi.responses import JSONResponse
            import uvicorn

            app = FastAPI(title="Zentao MCP Client Proxy", version="0.1.0")

            @app.post("/mcp/tools/{tool_name}")
            async def proxy_tool_call(tool_name: str, arguments: dict = None):
                """代理工具调用"""
                try:
                    if arguments is None:
                        arguments = {}
                    result = await self._forward_request(tool_name, arguments)
                    return JSONResponse(content=result)
                except Exception as e:
                    logger.error(f"工具调用失败: {e}")
                    return JSONResponse(
                        status_code=500,
                        content={"error": str(e)}
                    )

            @app.get("/health")
            async def health_check():
                """健康检查"""
                return {"status": "ok", "service": "zentao-mcp-client"}

            logger.info(f"启动HTTP服务器: http://{host}:{port}")
            config = uvicorn.Config(app, host=host, port=port, log_level="info")
            server = uvicorn.Server(config)
            await server.serve()

        except ImportError:
            logger.error("HTTP模式需要安装 fastapi 和 uvicorn")
            raise Exception("HTTP模式需要安装 fastapi 和 uvicorn")


def start_proxy_server(host: str, port: int, config: ClientConfig, mode: str = "stdio"):
    """启动代理服务器 - 支持多种模式"""

    proxy = ZentaoMCPProxy(config)
    try:
        if mode == "http":
            # HTTP服务器模式（由uvicorn/asyncio管理事件循环）
            asyncio.run(proxy.start_http_server(host, port))
        elif mode == "stdio":
            # STDIO模式（FastMCP内部使用anyio.run管理事件循环，外层不可再套 asyncio.run）
            logger.info("启动STDIO模式...")
            proxy.mcp.run(transport="stdio")
        elif mode == "sse":
            # SSE模式暂未完全实现，退回STDIO
            logger.warning("SSE模式暂未完全实现，使用STDIO模式")
            proxy.mcp.run(transport="sse",port=port)
        else:
            raise ValueError(f"不支持的模式: {mode}")
    except KeyboardInterrupt:
        logger.info("服务已停止")
    finally:
        # 关闭HTTP异步客户端
        try:
            asyncio.run(proxy.close())
        except RuntimeError:
            # 若当前线程已有事件循环或已关闭，忽略
            pass