[client]
# ==================================================
# Zentao MCP Client Configuration Example
# ==================================================

# 后端服务URL
# ----------------
# 这是您的Zentao MCP后端服务的地址。
# 示例: http://127.0.0.1:8000
# backend_url = http://your-zentao-mcp-backend-service-url

# API Key
# ---------
# 这是用于和后端服务认证的API Key。
# 请替换为您的有效API Key。
api_key = e775111355ee10cf8b813dd8db1721a88def9485b31cf9374c9638d219c8a279