#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简易连通性测试脚本：
- 读取客户端配置（支持 env/home/<USER>
- 调用后端 /api/v1/mcp/tools/mcp_get_health_status
- 使用 Authorization: Bearer {api_key}
"""
import sys
import json
import asyncio
import httpx
from zentao_mcp_client.config import ClientConfig

async def main() -> int:
    cfg = ClientConfig()
    info = cfg.get_config_info()
    backend = info.get("backend_url")
    api_key_preview = info.get("api_key_preview")
    src_backend = info.get("backend_url_source", "unknown")
    src_key = info.get("api_key_source", "unknown")

    print("=== 连通性测试 ===")
    print(f"- 后端URL: {backend} (source={src_backend})")
    print(f"- API Key: {api_key_preview} (source={src_key})")

    if not cfg.is_configured():
        print("❌ 配置不完整，请先配置后端URL与API Key（或通过环境变量覆盖）")
        return 1

    url = f"{backend}/api/v1/mcp/tools/mcp_get_health_status"
    headers = {
        "Authorization": f"Bearer {cfg.get_api_key()}",
        "Content-Type": "application/json",
    }

    async with httpx.AsyncClient(timeout=30.0) as client:
        try:
            resp = await client.post(url, json={}, headers=headers)
            print(f"- HTTP {resp.status_code}")
            try:
                data = resp.json()
            except Exception:
                data = {"raw": resp.text[:500]}
            print("- 响应: " + json.dumps(data, ensure_ascii=False))
            if resp.status_code == 200:
                print("✅ 连通性通过")
                return 0
            else:
                print("❌ 连通性失败，请检查API Key是否有效与活跃")
                return 2
        except httpx.HTTPError as e:
            print(f"❌ 请求失败: {e}")
            return 3

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))