# Zentao MCP Client - 轻量级禅道MCP客户端

[![Python Version](https://img.shields.io/badge/python-3.10+-blue.svg)](https://python.org)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![PyPI Version](https://img.shields.io/pypi/v/zentao-mcp-client.svg)](https://pypi.org/project/zentao-mcp-client/)

Zentao MCP Client 是一个轻量级的禅道MCP（Model Context Protocol）客户端代理，提供了简单易用的命令行界面和多种运行模式，让您可以轻松地与禅道MCP后端服务进行交互。

## ✨ 特性

- 🚀 **多种运行模式**: 支持STDIO、HTTP、SSE三种模式
- 🔧 **简单配置**: 交互式配置向导，一键设置
- 🛡️ **安全认证**: 支持API Key认证，保障数据安全
- 📦 **轻量级**: 最小化依赖，快速启动
- 🔄 **完整工具支持**: 支持所有26个禅道MCP工具
- 📊 **实时监控**: 内置健康检查和性能监控
- 🐳 **多种安装方式**: 支持pip安装、独立可执行文件

## 📋 系统要求

- Python 3.10 或更高版本
- 网络连接到禅道MCP后端服务

## 🚀 快速开始

### 方式一：通过pip安装（推荐）

```bash
# 从PyPI安装
pip install zentao-mcp-client

# 或从测试PyPI安装最新版本
pip install --index-url https://test.pypi.org/simple/ zentao-mcp-client
```

### 方式二：从源码安装

```bash
# 克隆仓库
git clone <repository-url>
cd zentao-mcp-client

# 安装依赖并安装
pip install -e .
```

## 🔧 配置

首次使用需要配置后端服务连接信息：

```bash
zentao-mcp-client configure
```

按提示输入：
- 后端服务URL（如：http://your-server:8000）
- API Key

## 使用

启动客户端代理服务：

```bash
zentao-mcp-client start
```

查看配置信息：

```bash
zentao-mcp-client info
```

## 功能特性

- 完全兼容原有的HTTP/STDIO/SSE使用方式
- 自动转发请求到云端后端服务
- 支持所有Zentao MCP工具和资源
- 简单的配置管理
- 轻量级设计，资源占用少

## 支持的工具

- 部门管理：获取部门列表、部门用户
- 项目管理：获取项目列表、项目需求、项目任务、项目Bug
- Bug管理：按时间范围查询、Bug详情
- 需求管理：需求详情
- 任务管理：任务详情
- 用户管理：用户信息查询