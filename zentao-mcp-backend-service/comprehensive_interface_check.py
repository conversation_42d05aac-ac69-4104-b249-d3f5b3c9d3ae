#!/usr/bin/env python3
"""
全面的接口检查脚本 - 分析所有MCP工具端点的实现状态
"""

import asyncio
import httpx
import json
import time
from typing import Dict, Any, List
from pathlib import Path

# 测试配置
BASE_URL = "http://127.0.0.1:8000"
API_KEY = "test-api-key-12345"

# 从check_interfaces.py中需要检查的接口
REQUIRED_INTERFACES = [
    # 直接接口
    "/api/v1/mcp/tools/zentao_get_users_by_account",
    "/api/v1/mcp/tools/zentao_get_tasks_by_account", 
    "/api/v1/mcp/tools/zentao_get_tasks_by_dept",
    "/api/v1/mcp/tools/zentao_get_bugs_by_time_and_dept",
    
    # 数据加工接口
    "/api/v1/mcp/tools/analyze_story_workload",
    "/api/v1/mcp/tools/analyze_bugs_by_dept_and_time",
    "/api/v1/mcp/tools/project_summary_analysis",
    "/api/v1/mcp/tools/personnel_workload_analysis",
    "/api/v1/mcp/tools/story_task_relation_query",
    "/api/v1/mcp/tools/bug_to_story_tracking",
    "/api/v1/mcp/tools/filter_bugs_by_criteria",
    "/api/v1/mcp/tools/batch_query_stories",
    "/api/v1/mcp/tools/validate_story_existence",
    
    # 系统工具
    "/api/v1/mcp/tools/mcp_get_health_status",
    "/api/v1/mcp/tools/mcp_get_performance_metrics"
]

# 从mcp_tools.py中已实现的接口
IMPLEMENTED_INTERFACES = [
    # 项目相关
    "/api/v1/mcp/tools/zentao_get_all_projects",
    "/api/v1/mcp/tools/zentao_get_project_detail",
    "/api/v1/mcp/tools/zentao_get_stories_by_project",
    "/api/v1/mcp/tools/zentao_get_tasks_by_project",
    "/api/v1/mcp/tools/zentao_get_bugs_by_project",
    
    # 任务相关
    "/api/v1/mcp/tools/zentao_get_task_detail",
    "/api/v1/mcp/tools/zentao_get_tasks_by_account",
    "/api/v1/mcp/tools/zentao_get_tasks_by_dept",
    
    # 需求相关
    "/api/v1/mcp/tools/zentao_get_story_detail",
    "/api/v1/mcp/tools/zentao_get_story_effort",
    "/api/v1/mcp/tools/zentao_check_story_exists",
    
    # 部门相关
    "/api/v1/mcp/tools/zentao_get_all_departments",
    "/api/v1/mcp/tools/zentao_get_users_by_department",
    
    # 用户相关
    "/api/v1/mcp/tools/zentao_get_user_info",
    "/api/v1/mcp/tools/zentao_get_users_by_account",
    
    # Bug相关
    "/api/v1/mcp/tools/zentao_get_bug_detail",
    "/api/v1/mcp/tools/zentao_get_bugs_by_time_and_dept",
    
    # 分析相关
    "/api/v1/mcp/tools/analyze_story_workload",
    "/api/v1/mcp/tools/analyze_bugs_by_dept_and_time",
    "/api/v1/mcp/tools/project_summary_analysis",
    "/api/v1/mcp/tools/personnel_workload_analysis",
    "/api/v1/mcp/tools/story_task_relation_query",
    "/api/v1/mcp/tools/bug_to_story_tracking",
    "/api/v1/mcp/tools/filter_bugs_by_criteria",
    "/api/v1/mcp/tools/batch_query_stories",
    "/api/v1/mcp/tools/validate_story_existence",
    
    # 系统工具
    "/api/v1/mcp/tools/mcp_get_health_status",
    "/api/v1/mcp/tools/mcp_get_performance_metrics",
]


async def check_interface_exists(client: httpx.AsyncClient, endpoint: str) -> Dict[str, Any]:
    """检查接口是否存在并可访问"""
    try:
        # 使用OPTIONS方法检查接口是否存在
        response = await client.options(endpoint, timeout=5.0)
        
        if response.status_code in [200, 405]:  # 405表示方法不允许但接口存在
            return {
                "endpoint": endpoint,
                "exists": True,
                "status_code": response.status_code,
                "method_check": "OPTIONS"
            }
        else:
            # 尝试POST方法（MCP工具接口都是POST）
            try:
                headers = {"Authorization": f"Bearer {API_KEY}", "Content-Type": "application/json"}
                response = await client.post(endpoint, json={}, headers=headers, timeout=5.0)
                
                return {
                    "endpoint": endpoint,
                    "exists": True,
                    "status_code": response.status_code,
                    "method_check": "POST",
                    "response_size": len(response.content),
                    "accessible": response.status_code not in [404, 405]
                }
            except Exception as post_error:
                return {
                    "endpoint": endpoint,
                    "exists": False,
                    "status_code": response.status_code,
                    "method_check": "OPTIONS",
                    "post_error": str(post_error)
                }
                
    except Exception as e:
        return {
            "endpoint": endpoint,
            "exists": False,
            "status_code": 0,
            "error": str(e)
        }


async def analyze_interfaces():
    """分析所有接口的实现状态"""
    print("🔍 开始全面接口分析...")
    print(f"目标服务器: {BASE_URL}")
    print("-" * 80)
    
    async with httpx.AsyncClient(base_url=BASE_URL) as client:
        # 检查所有已实现的接口
        print("📋 检查已实现的接口...")
        implemented_results = []
        for endpoint in IMPLEMENTED_INTERFACES:
            result = await check_interface_exists(client, endpoint)
            implemented_results.append(result)
            
            status = "✅" if result["exists"] else "❌"
            print(f"{status} {endpoint}")
        
        print(f"\n📋 检查需求中要求的接口...")
        required_results = []
        for endpoint in REQUIRED_INTERFACES:
            result = await check_interface_exists(client, endpoint)
            required_results.append(result)
            
            status = "✅" if result["exists"] else "❌"
            print(f"{status} {endpoint}")
        
        return implemented_results, required_results


def generate_analysis_report(implemented_results: List[Dict], required_results: List[Dict]):
    """生成分析报告"""
    print("\n" + "=" * 80)
    print("📊 接口实现状态分析报告")
    print("=" * 80)
    
    # 已实现接口统计
    impl_total = len(implemented_results)
    impl_exists = len([r for r in implemented_results if r["exists"]])
    impl_accessible = len([r for r in implemented_results if r.get("accessible", False)])
    
    print(f"已实现接口统计:")
    print(f"  总数: {impl_total}")
    print(f"  存在: {impl_exists} ({impl_exists/impl_total*100:.1f}%)")
    print(f"  可访问: {impl_accessible} ({impl_accessible/impl_total*100:.1f}%)")
    
    # 需求接口统计
    req_total = len(required_results)
    req_exists = len([r for r in required_results if r["exists"]])
    req_accessible = len([r for r in required_results if r.get("accessible", False)])
    
    print(f"\n需求接口统计:")
    print(f"  总数: {req_total}")
    print(f"  存在: {req_exists} ({req_exists/req_total*100:.1f}%)")
    print(f"  可访问: {req_accessible} ({req_accessible/req_total*100:.1f}%)")
    
    # 找出缺失的接口
    required_endpoints = set(REQUIRED_INTERFACES)
    implemented_endpoints = set(IMPLEMENTED_INTERFACES)
    
    missing_in_implementation = required_endpoints - implemented_endpoints
    extra_in_implementation = implemented_endpoints - required_endpoints
    
    print(f"\n🔍 接口对比分析:")
    print(f"  需求中要求但未实现: {len(missing_in_implementation)}")
    print(f"  已实现但需求中未提及: {len(extra_in_implementation)}")
    
    if missing_in_implementation:
        print(f"\n❌ 缺失的接口:")
        for endpoint in sorted(missing_in_implementation):
            print(f"  - {endpoint}")
    
    if extra_in_implementation:
        print(f"\n➕ 额外实现的接口:")
        for endpoint in sorted(extra_in_implementation):
            print(f"  - {endpoint}")
    
    # 找出不可访问的接口
    inaccessible_impl = [r for r in implemented_results if r["exists"] and not r.get("accessible", False)]
    inaccessible_req = [r for r in required_results if r["exists"] and not r.get("accessible", False)]
    
    if inaccessible_impl or inaccessible_req:
        print(f"\n⚠️  存在但不可访问的接口:")
        for result in inaccessible_impl + inaccessible_req:
            print(f"  - {result['endpoint']} (状态码: {result['status_code']})")
    
    return {
        "implemented": {
            "total": impl_total,
            "exists": impl_exists,
            "accessible": impl_accessible,
            "results": implemented_results
        },
        "required": {
            "total": req_total,
            "exists": req_exists,
            "accessible": req_accessible,
            "results": required_results
        },
        "analysis": {
            "missing_in_implementation": list(missing_in_implementation),
            "extra_in_implementation": list(extra_in_implementation),
            "inaccessible": [r["endpoint"] for r in inaccessible_impl + inaccessible_req]
        }
    }


def suggest_cleanup_actions(report: Dict):
    """建议清理操作"""
    print(f"\n🧹 建议的清理操作:")
    print("-" * 40)
    
    # 可以删除的测试文件
    test_files_to_remove = [
        "test_missing_interfaces.py",
        "test_missing_interfaces_result.json",
        "quick_test.py",
        "test_api.py",
        "test_complete.py",
    ]
    
    print("📁 可以删除的测试文件:")
    for file in test_files_to_remove:
        file_path = Path(file)
        if file_path.exists():
            print(f"  ✅ {file} (存在，可删除)")
        else:
            print(f"  ⚪ {file} (不存在)")
    
    # 需要保留的核心文件
    core_files_to_keep = [
        "run_complete_test.py",
        "tests/",
        "comprehensive_interface_check.py"
    ]
    
    print(f"\n📁 需要保留的核心文件:")
    for file in core_files_to_keep:
        print(f"  🔒 {file}")
    
    # 需要修改的文件
    if report["analysis"]["missing_in_implementation"]:
        print(f"\n🔧 需要修改的文件:")
        print(f"  - app/api/v1/endpoints/mcp_tools.py (添加缺失接口)")
        print(f"  - 客户端工具端点映射表 (同步更新)")


async def main():
    """主函数"""
    try:
        # 分析接口
        implemented_results, required_results = await analyze_interfaces()
        
        # 生成报告
        report = generate_analysis_report(implemented_results, required_results)
        
        # 建议清理操作
        suggest_cleanup_actions(report)
        
        # 保存详细结果
        output_data = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "base_url": BASE_URL,
            "report": report
        }
        
        with open("comprehensive_interface_analysis.json", "w", encoding="utf-8") as f:
            json.dump(output_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 详细分析结果已保存到: comprehensive_interface_analysis.json")
        
        # 返回成功状态
        success_rate = (report["required"]["accessible"] / report["required"]["total"]) * 100
        return success_rate >= 90.0
        
    except Exception as e:
        print(f"\n💥 分析执行失败: {e}")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
