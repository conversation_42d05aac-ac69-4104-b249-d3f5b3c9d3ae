"""
Zentao MCP Backend Service - FastAPI Application Entry Point
"""
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from app.core.config import settings
from app.core.init import initialize_system
from app.core.database import Base, engine
from app.core.logging import setup_logging, app_logger
from app.core.exceptions import (
    UserManagementException, PermissionDeniedError
)
from app.middleware.logging import RequestLoggingMiddleware
from app.middleware.exception import ExceptionHandlingMiddleware, setup_exception_handlers

# 设置日志系统
setup_logging(
    log_level=settings.log_level,
    log_file=settings.log_file if not settings.debug else None
)

app = FastAPI(
    title="Zentao MCP Backend Service",
    description="Cloud-based API service for Zentao MCP operations",
    version="0.1.0",
)

# 添加中间件
app.add_middleware(ExceptionHandlingMiddleware)
app.add_middleware(RequestLoggingMiddleware)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 包含管理员Web界面API路由（使用会话认证）
from app.api.v1.api import api_router
app.include_router(api_router, prefix="/api/v1/admin", tags=["Admin Web Interface"])

# 包含MCP工具和资源API路由
from app.api.v1.endpoints.zentao.mcp_tools import router as mcp_tools_router
from app.api.v1.endpoints.zentao.mcp_resources import router as mcp_resources_router
app.include_router(mcp_tools_router, prefix="/api/v1/mcp/tools", tags=["MCP Tools"])
app.include_router(mcp_resources_router, prefix="/api/v1/mcp/resources", tags=["MCP Resources"])

# 设置全局异常处理器
setup_exception_handlers(app)

# 保留原有的异常处理器作为备用
@app.exception_handler(UserManagementException)
async def user_management_exception_handler(request, exc: UserManagementException):
    """处理用户管理相关异常"""
    request_id = getattr(request.state, 'request_id', 'unknown')
    app_logger.warning(
        f"User management exception: {str(exc)}",
        extra={
            "request_id": request_id,
            "endpoint": request.url.path,
            "method": request.method,
            "error": str(exc),
            "error_type": type(exc).__name__
        }
    )
    return JSONResponse(
        status_code=400,
        content={
            "error": True,
            "message": str(exc),
            "request_id": request_id,
            "error_type": type(exc).__name__
        }
    )

@app.exception_handler(PermissionDeniedError)
async def permission_denied_exception_handler(request, exc: PermissionDeniedError):
    """处理权限不足异常"""
    request_id = getattr(request.state, 'request_id', 'unknown')
    app_logger.warning(
        f"Permission denied: {str(exc)}",
        extra={
            "request_id": request_id,
            "endpoint": request.url.path,
            "method": request.method,
            "error": str(exc),
            "error_type": "PermissionDeniedError"
        }
    )
    return JSONResponse(
        status_code=403,
        content={
            "error": True,
            "message": str(exc),
            "request_id": request_id,
            "error_type": "PermissionDeniedError"
        }
    )


@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    # 创建数据库表
    Base.metadata.create_all(bind=engine)
    
    # 初始化系统
    if initialize_system():
        app_logger.info("系统初始化完成")
    else:
        app_logger.error("系统初始化失败")
    
    app_logger.info("Zentao MCP Backend Service 启动完成")


@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    from app.zentao_engine.client import get_zentao_service
    try:
        zentao_service = await get_zentao_service()
        await zentao_service.close()
    except Exception as e:
        app_logger.warning(f"关闭Zentao服务时出现异常: {e}")
    app_logger.info("Zentao MCP Backend Service 已关闭")


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return JSONResponse(
        status_code=200,
        content={
            "status": "healthy",
            "service": "zentao-mcp-backend-service",
            "version": "0.1.0"
        }
    )


@app.get("/")
async def root():
    """Root endpoint"""
    return JSONResponse(
        content={
            "message": "Zentao MCP Backend Service",
            "version": "0.1.0",
            "docs": "/docs"
        }
    )


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)