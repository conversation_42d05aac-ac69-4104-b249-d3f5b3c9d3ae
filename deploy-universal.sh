#!/bin/bash

# 禅道MCP服务通用部署脚本 - 支持Docker和Podman

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 全局变量
CONTAINER_ENGINE=""
COMPOSE_CMD=""
PROJECT_NAME="zentao-mcp"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
禅道MCP服务通用部署脚本 - 支持Docker和Podman

用法: $0 [选项] [环境] [操作]

环境:
    dev         开发环境
    prod        生产环境
    test        测试环境

操作:
    deploy      部署服务 (默认)
    start       启动服务
    stop        停止服务
    restart     重启服务
    status      查看状态
    logs        查看日志
    clean       清理环境
    build       构建镜像

选项:
    -h, --help          显示帮助信息
    -e, --engine ENGINE 指定容器引擎 (docker|podman)
    -v, --verbose       详细输出
    -f, --force         强制执行
    --no-cache          构建时不使用缓存
    --pull              拉取最新基础镜像

示例:
    $0 dev deploy                    # 部署开发环境
    $0 prod deploy --engine docker   # 使用Docker部署生产环境
    $0 dev logs                      # 查看开发环境日志
    $0 prod clean --force            # 强制清理生产环境
EOF
}

# 检测容器引擎
detect_container_engine() {
    if [[ -n "$CONTAINER_ENGINE" ]]; then
        log_info "使用指定的容器引擎: $CONTAINER_ENGINE"
        return
    fi

    if command -v podman &> /dev/null; then
        CONTAINER_ENGINE="podman"
        log_info "检测到Podman，使用Podman作为容器引擎"
    elif command -v docker &> /dev/null; then
        CONTAINER_ENGINE="docker"
        log_info "检测到Docker，使用Docker作为容器引擎"
    else
        log_error "未找到Docker或Podman，请先安装容器引擎"
        exit 1
    fi
}

# 设置compose命令
setup_compose_command() {
    case $CONTAINER_ENGINE in
        podman)
            if command -v podman-compose &> /dev/null; then
                COMPOSE_CMD="podman-compose"
            elif command -v docker-compose &> /dev/null; then
                COMPOSE_CMD="docker-compose"
                log_warning "使用docker-compose与podman，可能存在兼容性问题"
            else
                log_error "未找到podman-compose或docker-compose"
                exit 1
            fi
            ;;
        docker)
            if docker compose version &> /dev/null; then
                COMPOSE_CMD="docker compose"
            elif command -v docker-compose &> /dev/null; then
                COMPOSE_CMD="docker-compose"
            else
                log_error "未找到docker compose或docker-compose"
                exit 1
            fi
            ;;
    esac
    
    log_info "使用Compose命令: $COMPOSE_CMD"
}

# 检查环境文件
check_env_file() {
    local env=$1
    local env_file=".env.${env}"
    
    if [[ ! -f "$env_file" ]]; then
        log_warning "环境文件 $env_file 不存在，创建默认配置..."
        create_default_env_file "$env" "$env_file"
    fi
    
    log_info "使用环境文件: $env_file"
}

# 创建默认环境文件
create_default_env_file() {
    local env=$1
    local env_file=$2
    
    cat > "$env_file" << EOF
# 禅道MCP ${env} 环境配置

# 数据库配置
POSTGRES_DB=zentao_mcp_${env}
POSTGRES_USER=zentao_mcp_${env}
POSTGRES_PASSWORD=change_me_in_production_$(date +%s)

# 应用配置
SECRET_KEY=change_me_in_production_$(openssl rand -hex 32 2>/dev/null || date +%s)
DEBUG=false
CORS_ORIGINS=http://localhost:3000

# Redis配置
REDIS_PASSWORD=change_me_in_production_$(date +%s)

# 监控配置
GRAFANA_PASSWORD=admin_$(date +%s)

# 外部服务配置
SENTRY_DSN=
EOF
    
    log_warning "请编辑 $env_file 文件，设置正确的配置值"
}

# 获取compose文件参数
get_compose_files() {
    local env=$1
    local compose_files="-f docker-compose.yml"
    
    case $env in
        dev)
            compose_files="$compose_files -f docker-compose.dev.yml"
            ;;
        prod)
            compose_files="$compose_files -f docker-compose.prod.yml"
            ;;
        test)
            compose_files="$compose_files -f docker-compose.test.yml"
            ;;
    esac
    
    echo "$compose_files"
}

# 构建镜像
build_images() {
    local env=$1
    local build_args=""
    
    if [[ "$NO_CACHE" == "true" ]]; then
        build_args="$build_args --no-cache"
    fi
    
    if [[ "$PULL" == "true" ]]; then
        build_args="$build_args --pull"
    fi
    
    log_info "构建 $env 环境镜像..."
    
    local compose_files=$(get_compose_files "$env")
    $COMPOSE_CMD $compose_files --env-file ".env.${env}" build $build_args
    
    log_success "镜像构建完成"
}

# 部署服务
deploy_services() {
    local env=$1
    
    log_info "部署 $env 环境服务..."
    
    local compose_files=$(get_compose_files "$env")
    
    # 创建网络（如果不存在）
    if [[ "$CONTAINER_ENGINE" == "podman" ]]; then
        podman network exists zentao-mcp-network || podman network create zentao-mcp-network
    fi
    
    # 启动服务
    $COMPOSE_CMD $compose_files --env-file ".env.${env}" up -d
    
    log_success "服务部署完成"
}

# 启动服务
start_services() {
    local env=$1
    
    log_info "启动 $env 环境服务..."
    
    local compose_files=$(get_compose_files "$env")
    $COMPOSE_CMD $compose_files --env-file ".env.${env}" start
    
    log_success "服务启动完成"
}

# 停止服务
stop_services() {
    local env=$1
    
    log_info "停止 $env 环境服务..."
    
    local compose_files=$(get_compose_files "$env")
    $COMPOSE_CMD $compose_files --env-file ".env.${env}" stop
    
    log_success "服务停止完成"
}

# 重启服务
restart_services() {
    local env=$1
    
    log_info "重启 $env 环境服务..."
    
    stop_services "$env"
    start_services "$env"
    
    log_success "服务重启完成"
}

# 查看服务状态
show_status() {
    local env=$1
    
    log_info "查看 $env 环境服务状态..."
    
    local compose_files=$(get_compose_files "$env")
    $COMPOSE_CMD $compose_files --env-file ".env.${env}" ps
    
    echo
    log_info "服务访问地址:"
    case $env in
        dev)
            echo "  前端: http://localhost:5173"
            echo "  后端: http://localhost:8001"
            ;;
        *)
            echo "  前端: http://localhost:3000"
            echo "  后端: http://localhost:8000"
            ;;
    esac
    echo "  API文档: http://localhost:8000/docs"
}

# 查看日志
show_logs() {
    local env=$1
    local service=$2
    
    log_info "查看 $env 环境日志..."
    
    local compose_files=$(get_compose_files "$env")
    
    if [[ -n "$service" ]]; then
        $COMPOSE_CMD $compose_files --env-file ".env.${env}" logs -f "$service"
    else
        $COMPOSE_CMD $compose_files --env-file ".env.${env}" logs -f
    fi
}

# 清理环境
clean_environment() {
    local env=$1
    
    if [[ "$FORCE" != "true" ]]; then
        read -p "确认清理 $env 环境? 这将删除所有容器和数据 (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "取消清理操作"
            return
        fi
    fi
    
    log_info "清理 $env 环境..."
    
    local compose_files=$(get_compose_files "$env")
    $COMPOSE_CMD $compose_files --env-file ".env.${env}" down --volumes --remove-orphans
    
    # 清理未使用的镜像和卷
    if [[ "$CONTAINER_ENGINE" == "docker" ]]; then
        docker system prune -f
    elif [[ "$CONTAINER_ENGINE" == "podman" ]]; then
        podman system prune -f
    fi
    
    log_success "环境清理完成"
}

# 等待服务就绪
wait_for_services() {
    local env=$1
    local max_attempts=60
    local attempt=1
    
    log_info "等待服务就绪..."
    
    local backend_port=8000
    if [[ "$env" == "dev" ]]; then
        backend_port=8001
    fi
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -f "http://localhost:${backend_port}/health" &> /dev/null; then
            log_success "服务就绪"
            return 0
        fi
        
        log_info "等待服务启动... ($attempt/$max_attempts)"
        sleep 5
        ((attempt++))
    done
    
    log_error "服务启动超时"
    return 1
}

# 主函数
main() {
    local environment=""
    local operation="deploy"
    local service=""
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -e|--engine)
                CONTAINER_ENGINE="$2"
                shift 2
                ;;
            -v|--verbose)
                set -x
                shift
                ;;
            -f|--force)
                FORCE=true
                shift
                ;;
            --no-cache)
                NO_CACHE=true
                shift
                ;;
            --pull)
                PULL=true
                shift
                ;;
            dev|prod|test)
                environment=$1
                shift
                ;;
            deploy|start|stop|restart|status|logs|clean|build)
                operation=$1
                shift
                ;;
            *)
                if [[ -z "$service" && "$operation" == "logs" ]]; then
                    service=$1
                else
                    log_error "未知参数: $1"
                    show_help
                    exit 1
                fi
                shift
                ;;
        esac
    done
    
    # 检查环境参数
    if [[ -z "$environment" ]]; then
        log_error "请指定部署环境 (dev/prod/test)"
        show_help
        exit 1
    fi
    
    # 检测容器引擎
    detect_container_engine
    setup_compose_command
    
    # 检查环境文件
    check_env_file "$environment"
    
    # 执行操作
    case $operation in
        deploy)
            build_images "$environment"
            deploy_services "$environment"
            wait_for_services "$environment"
            show_status "$environment"
            ;;
        build)
            build_images "$environment"
            ;;
        start)
            start_services "$environment"
            ;;
        stop)
            stop_services "$environment"
            ;;
        restart)
            restart_services "$environment"
            ;;
        status)
            show_status "$environment"
            ;;
        logs)
            show_logs "$environment" "$service"
            ;;
        clean)
            clean_environment "$environment"
            ;;
        *)
            log_error "未知操作: $operation"
            show_help
            exit 1
            ;;
    esac
    
    log_success "操作完成!"
}

# 执行主函数
main "$@"
