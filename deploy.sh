#!/bin/bash

# Zentao MCP 服务部署脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
Zentao MCP 服务部署脚本

用法: $0 [选项] [环境]

环境:
    dev         开发环境部署
    prod        生产环境部署
    test        测试环境部署

选项:
    -h, --help      显示帮助信息
    -v, --verbose   详细输出
    -c, --clean     清理现有容器和卷
    -b, --build     强制重新构建镜像
    --no-cache      构建时不使用缓存
    --pull          拉取最新基础镜像

示例:
    $0 dev                  # 部署开发环境
    $0 prod --build         # 重新构建并部署生产环境
    $0 dev --clean          # 清理并部署开发环境
EOF
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 检查环境文件
check_env_file() {
    local env=$1
    local env_file=".env.${env}"
    
    if [[ ! -f "$env_file" ]]; then
        log_warning "环境文件 $env_file 不存在，创建默认配置..."
        create_default_env_file "$env" "$env_file"
    fi
    
    log_info "使用环境文件: $env_file"
}

# 创建默认环境文件
create_default_env_file() {
    local env=$1
    local env_file=$2
    
    cat > "$env_file" << EOF
# Zentao MCP ${env} 环境配置

# 数据库配置
POSTGRES_DB=zentao_mcp_${env}
POSTGRES_USER=zentao_mcp_${env}
POSTGRES_PASSWORD=change_me_in_production

# 应用配置
SECRET_KEY=change_me_in_production
DEBUG=false
CORS_ORIGINS=http://localhost:3000

# Redis配置
REDIS_PASSWORD=change_me_in_production

# 监控配置
GRAFANA_PASSWORD=admin

# 外部服务配置
SENTRY_DSN=
EOF
    
    log_warning "请编辑 $env_file 文件，设置正确的配置值"
}

# 清理环境
clean_environment() {
    log_info "清理现有环境..."
    
    docker-compose down --volumes --remove-orphans || true
    docker system prune -f || true
    
    log_success "环境清理完成"
}

# 构建镜像
build_images() {
    local build_args=""
    
    if [[ "$NO_CACHE" == "true" ]]; then
        build_args="$build_args --no-cache"
    fi
    
    if [[ "$PULL" == "true" ]]; then
        build_args="$build_args --pull"
    fi
    
    log_info "构建镜像..."
    docker-compose build $build_args
    log_success "镜像构建完成"
}

# 部署服务
deploy_services() {
    local env=$1
    local compose_files="-f docker-compose.yml"
    
    # 根据环境选择compose文件
    case $env in
        dev)
            compose_files="$compose_files -f docker-compose.dev.yml"
            ;;
        prod)
            compose_files="$compose_files -f docker-compose.prod.yml"
            ;;
        test)
            compose_files="$compose_files -f docker-compose.test.yml"
            ;;
    esac
    
    log_info "部署 $env 环境服务..."
    
    # 启动服务
    docker-compose $compose_files --env-file ".env.${env}" up -d
    
    log_success "服务部署完成"
}

# 等待服务就绪
wait_for_services() {
    log_info "等待服务就绪..."
    
    local max_attempts=30
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -f http://localhost:8000/health &> /dev/null; then
            log_success "后端服务就绪"
            break
        fi
        
        log_info "等待后端服务启动... ($attempt/$max_attempts)"
        sleep 5
        ((attempt++))
    done
    
    if [[ $attempt -gt $max_attempts ]]; then
        log_error "服务启动超时"
        return 1
    fi
}

# 运行数据库迁移
run_migrations() {
    log_info "运行数据库迁移..."
    
    docker-compose exec backend python -m alembic upgrade head
    
    log_success "数据库迁移完成"
}

# 显示部署状态
show_status() {
    log_info "服务状态:"
    docker-compose ps
    
    echo
    log_info "服务访问地址:"
    echo "  前端: http://localhost:3000"
    echo "  后端: http://localhost:8000"
    echo "  API文档: http://localhost:8000/docs"
}

# 主函数
main() {
    local environment=""
    local clean=false
    local build=false
    local verbose=false
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -v|--verbose)
                verbose=true
                set -x
                shift
                ;;
            -c|--clean)
                clean=true
                shift
                ;;
            -b|--build)
                build=true
                shift
                ;;
            --no-cache)
                NO_CACHE=true
                shift
                ;;
            --pull)
                PULL=true
                shift
                ;;
            dev|prod|test)
                environment=$1
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 检查环境参数
    if [[ -z "$environment" ]]; then
        log_error "请指定部署环境 (dev/prod/test)"
        show_help
        exit 1
    fi
    
    log_info "开始部署 Zentao MCP 服务 ($environment 环境)"
    
    # 执行部署步骤
    check_dependencies
    check_env_file "$environment"
    
    if [[ "$clean" == "true" ]]; then
        clean_environment
    fi
    
    if [[ "$build" == "true" ]]; then
        build_images
    fi
    
    deploy_services "$environment"
    wait_for_services
    run_migrations
    show_status
    
    log_success "部署完成!"
}

# 执行主函数
main "$@"
